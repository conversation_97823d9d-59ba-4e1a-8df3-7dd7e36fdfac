import { ref, computed, reactive, nextTick, onMounted, onUnmounted } from 'vue';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, GroupLayer, LayerType } from '../types';
import { GeoServerService } from '../GeoServerService';
import { MapManager } from '../map';
import { getGeoServerCapabilitiesUrl } from '../config';

/**
 * Enhanced composable for managing layers with full reactive state management.
 * This replaces the old LayerManager class with a Vue 3 composable approach.
 *
 * Features:
 * - Reactive layer and group state
 * - Automatic legend management
 * - Layer ordering and z-index management
 * - Parent-child visibility relationships
 * - Loading and error states
 */
export function useLayers(mapManager: MapManager | null) {
  // Reactive state
  const layers = ref<GeoLayer[]>([]);
  const groups = ref<GroupLayer[]>([]);
  const isLoading = ref(false);
  const error = ref('');

  const geoServerService = new GeoServerService();

  // Computed properties for derived state (automatically reactive)
  const rootGroups = computed(() => {
    return groups.value.filter(group => !group.parent);
  });

  const ungroupedLayers = computed(() => {
    return layers.value.filter(layer => !layer.groupPath);
  });

  // Computed property for visible layers (used for legend)
  const visibleLayers = computed(() => {
    return layers.value.filter(layer => layer.visible);
  });

  // Computed property for all layers sorted by z-index
  const layersByZIndex = computed(() => {
    return [...layers.value].sort((a, b) => a.zIndex - b.zIndex);
  });

  const fetchLayers = async () => {
    if (!mapManager) return;

    isLoading.value = true;
    error.value = '';

    try {
      console.log('Starting to fetch layers from GeoServer...');
      console.log('GeoServer URL:', getGeoServerCapabilitiesUrl());

      const fetchedLayers = await geoServerService.fetchLayers();
      console.log('Fetched layers count:', fetchedLayers.length);
      console.log('Fetched layers:', fetchedLayers);

      if (fetchedLayers.length > 0) {
        organizeLayersIntoGroups(fetchedLayers);
        fetchedLayers.forEach(layer => {
          layers.value.push(layer);
          mapManager.addLayer(layer);
        });

        console.log('Updated reactive layers:', layers.value);
        console.log('Updated root groups:', rootGroups.value);
        console.log('Updated ungrouped layers:', ungroupedLayers.value);
      } else {
        console.warn('No layers found in GeoServer');
      }
    } catch (err) {
      console.error('Error fetching layers:', err);
      error.value = 'Error cargando capas desde GeoServer.';
    } finally {
      isLoading.value = false;
    }
  };

  const organizeLayersIntoGroups = (fetchedLayers: GeoLayer[]): void => {
    groups.value = [];
    const groupMap = new Map<string, GroupLayer>();

    fetchedLayers.forEach(layer => {
      if (!layer.groupPath) return;

      const groupNames = layer.groupPath.split('/');
      let currentPath = '';
      let parentGroup: GroupLayer | undefined;
      let level = 0;

      groupNames.forEach((groupName: string) => {
        currentPath = currentPath ? `${currentPath}/${groupName}` : groupName;
        let group = groupMap.get(currentPath);

        if (!group) {
          group = {
            id: `group_${currentPath.replace(/\//g, '_')}`,
            name: groupName,
            children: [] as (GeoLayer | GroupLayer)[],
            collapsed: true,
            visible: false,
            level,
            parent: parentGroup,
          } as GroupLayer;
          groups.value.push(group);
          groupMap.set(currentPath, group);
          if (parentGroup) {
            parentGroup.children.push(group);
          }
        }
        parentGroup = group;
        level++;
      });

      layer.parent = parentGroup;
      if (parentGroup) {
        parentGroup.children.push(layer);
      }
    });
  };

  const toggleGroupCollapse = (groupId: string): void => {
    console.log('Toggling group collapse for:', groupId);
    const group = groups.value.find(g => g.id === groupId);
    if (group) {
      console.log('Group found, current collapsed state:', group.collapsed);
      group.collapsed = !group.collapsed;
      console.log('New collapsed state:', group.collapsed);
    } else {
      console.log('Group not found:', groupId);
    }
  };

  const toggleGroupVisibility = (groupId: string): void => {
    const group = groups.value.find(g => g.id === groupId);
    if (!group) return;

    const newVisibility = !group.visible;
    console.log(`Toggling group ${group.name} visibility from ${group.visible} to ${newVisibility}`);

    group.visible = newVisibility;
    updateChildrenVisibility(group, newVisibility);

    console.log(`Group ${group.name} visibility updated.`);
  };

  const updateChildrenVisibility = (group: GroupLayer, visible: boolean): void => {
    group.children.forEach((child: GeoLayer | GroupLayer) => {
      if ('children' in child) {
        const childGroup = child as GroupLayer;
        childGroup.visible = visible;
        updateChildrenVisibility(childGroup, visible);
      } else {
        const layer = child as GeoLayer;
        // Update layer visibility directly without triggering parent updates to avoid recursion
        layer.visible = visible;
        if (mapManager) {
          mapManager.toggleLayerVisibility(layer.id, visible);
        }
      }
    });
  };

  const toggleLayerVisibility = (layerId: string, visible?: boolean): void => {
    const layer = layers.value.find(l => l.id === layerId);
    if (!layer || !mapManager) return;

    const newVisibility = visible !== undefined ? visible : !layer.visible;
    console.log(`Toggling layer ${layer.name} visibility from ${layer.visible} to ${newVisibility}`);

    layer.visible = newVisibility;
    mapManager.toggleLayerVisibility(layerId, newVisibility);

    // Bring layer to top when made visible (as per user requirements)
    if (newVisibility) {
      bringLayerToTop(layerId);
    }

    // Update parent group states based on children visibility
    updateParentGroupStates(layer);

    console.log(`Layer ${layer.name} visibility updated.`);
  };

  const updateParentGroupStates = (layer: GeoLayer): void => {
    if (!layer.parent) return;

    const parent = layer.parent;
    const childLayers = parent.children.filter(child => !('children' in child)) as GeoLayer[];
    const childGroups = parent.children.filter(child => 'children' in child) as GroupLayer[];

    // Check if all children (layers and groups) are visible
    const allChildLayersVisible = childLayers.every(child => child.visible);
    const allChildGroupsVisible = childGroups.every(child => child.visible);
    const allChildrenVisible = allChildLayersVisible && allChildGroupsVisible;

    // Check if any children (layers and groups) are visible
    const anyChildLayersVisible = childLayers.some(child => child.visible);
    const anyChildGroupsVisible = childGroups.some(child => child.visible);
    const anyChildrenVisible = anyChildLayersVisible || anyChildGroupsVisible;

    const oldParentVisibility = parent.visible;

    // Update parent visibility based on children
    if (allChildrenVisible) {
      parent.visible = true;
    } else if (!anyChildrenVisible) {
      parent.visible = false;
    }
    // For partial visibility, we keep the current state or could implement indeterminate logic

    if (oldParentVisibility !== parent.visible) {
      console.log(`Updated parent group ${parent.name} visibility from ${oldParentVisibility} to ${parent.visible}`);
    }

    // Recursively update grandparent states
    updateParentGroupStates(parent as any);
  };

  const updateLayerOpacity = (layerId: string, opacity: number): void => {
    const layer = layers.value.find(l => l.id === layerId);
    if (!layer || !mapManager) return;

    layer.opacity = opacity;
    mapManager.updateLayerOpacity(layerId, opacity);
  };

  /**
   * Brings a layer to the top of the layer stack without incrementing z-index unnecessarily.
   * Uses the MapManager's bringToFront functionality.
   */
  const bringLayerToTop = (layerId: string): void => {
    if (!mapManager) return;
    mapManager.bringLayerToTop(layerId);
  };

  /**
   * Reorders layers by updating their z-index values.
   * This is more efficient than constantly incrementing z-index.
   */
  const reorderLayers = (layerIds: string[]): void => {
    layerIds.forEach((layerId, index) => {
      const layer = layers.value.find(l => l.id === layerId);
      if (layer) {
        layer.zIndex = index + 1;
        if (mapManager && layer.layer_instance) {
          // Update the layer's z-index on the map if needed
          if ('setZIndex' in layer.layer_instance) {
            (layer.layer_instance as any).setZIndex(layer.zIndex);
          }
        }
      }
    });
  };

  /**
   * Gets a layer by its ID
   */
  const getLayerById = (layerId: string): GeoLayer | undefined => {
    return layers.value.find(l => l.id === layerId);
  };

  /**
   * Gets a group by its ID
   */
  const getGroupById = (groupId: string): GroupLayer | undefined => {
    return groups.value.find(g => g.id === groupId);
  };

  /**
   * Clears all layers and groups
   */
  const clearLayers = (): void => {
    layers.value.forEach(layer => {
      if (mapManager && layer.layer_instance) {
        mapManager.removeLayer(layer.id);
      }
    });
    layers.value = [];
    groups.value = [];
  };

  // Initialize layers when mapManager is available
  const initialize = async () => {
    if (mapManager) {
      await fetchLayers();
    }
  };

  // Cleanup function
  const cleanup = (): void => {
    clearLayers();
  };

  // Cleanup on unmount
  onUnmounted(() => {
    cleanup();
  });

  return {
    // Reactive state
    layers,
    groups,
    rootGroups,
    ungroupedLayers,
    visibleLayers,
    layersByZIndex,
    isLoading,
    error,

    // Core methods
    initialize,
    cleanup,
    fetchLayers,

    // Group methods
    toggleGroupCollapse,
    toggleGroupVisibility,

    // Layer methods
    toggleLayerVisibility,
    updateLayerOpacity,
    bringLayerToTop,
    reorderLayers,

    // Utility methods
    getLayerById,
    getGroupById,
    clearLayers,
    getLayers: () => layers.value,
    getRootGroups: () => rootGroups.value,
    getUngroupedLayers: () => ungroupedLayers.value,
    getVisibleLayers: () => visibleLayers.value
  };
}

// For backward compatibility, export the old name as well
export const useLayerManager = useLayers;
