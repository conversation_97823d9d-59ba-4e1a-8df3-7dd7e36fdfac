// Simple test to verify useLayers composable functionality
// This can be run in the browser console to test the layer management

function testLayerManager() {
  const layerManager = window.layerManager;

  if (!layerManager) {
    console.error('LayerManager (useLayers composable) not found on window object');
    return;
  }

  console.log('Testing useLayers composable functionality...');
  
  // Test 1: Check initial state
  console.log('Initial layers:', layerManager.layers.value);
  console.log('Initial root groups:', layerManager.rootGroups.value);
  console.log('Initial ungrouped layers:', layerManager.ungroupedLayers.value);
  
  // Test 2: Toggle group collapse
  const firstGroup = layerManager.rootGroups.value[0];
  if (firstGroup) {
    console.log('Testing group collapse...');
    const initialCollapsed = firstGroup.collapsed;
    layerManager.toggleGroupCollapse(firstGroup.id);
    console.log(`Group ${firstGroup.name} collapsed state changed from ${initialCollapsed} to ${firstGroup.collapsed}`);
  }
  
  // Test 3: Toggle layer visibility
  const firstLayer = layerManager.layers.value[0];
  if (firstLayer) {
    console.log('Testing layer visibility...');
    const initialVisible = firstLayer.visible;
    layerManager.toggleLayerVisibility(firstLayer.id);
    console.log(`Layer ${firstLayer.name} visibility changed from ${initialVisible} to ${firstLayer.visible}`);
  }
  
  // Test 4: Toggle group visibility
  if (firstGroup) {
    console.log('Testing group visibility...');
    const initialVisible = firstGroup.visible;
    layerManager.toggleGroupVisibility(firstGroup.id);
    console.log(`Group ${firstGroup.name} visibility changed from ${initialVisible} to ${firstGroup.visible}`);
  }
  
  console.log('useLayers composable test completed!');
}

// Export for use in browser console
window.testLayerManager = testLayerManager;

console.log('Test script loaded. Run testLayerManager() in console to test.');
